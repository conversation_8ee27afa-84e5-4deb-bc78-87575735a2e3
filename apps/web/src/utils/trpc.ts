import { QueryCache, QueryClient } from '@tanstack/react-query';
import { createTRPCReact } from '@trpc/react-query';
import { httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../../../server/src/routers/index';
import { toast } from 'sonner';

export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error: Error) => {
      toast.error(error.message, {
        action: {
          label: "retry",
          onClick: () => {
            queryClient.invalidateQueries();
          },
        },
      });
    },
  }),
});

export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: `${process.env.NEXT_PUBLIC_SERVER_URL}/trpc`,
      // Add debugging and error handling
      fetch: async (url, options) => {
        console.log('🔗 tRPC Request:', {
          url,
          method: options?.method,
          serverUrl: process.env.NEXT_PUBLIC_SERVER_URL,
          fullUrl: `${process.env.NEXT_PUBLIC_SERVER_URL}/trpc`
        });

        try {
          const response = await fetch(url, options);
          console.log('📡 tRPC Response:', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            url: response.url
          });

          if (!response.ok) {
            const text = await response.text();
            console.error('❌ tRPC Error Response:', text);
          }

          return response;
        } catch (error) {
          console.error('🚨 tRPC Fetch Error:', error);
          throw error;
        }
      },
    }),
  ],
});


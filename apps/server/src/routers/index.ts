import { z } from "zod";
import {
  publicProcedure,
  protectedProcedure,
  router,
} from "../lib/trpc";
import { db, tasks, milestones, users } from "../db";
import { eq, and, desc, asc, isNull } from "drizzle-orm";
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateObject } from 'ai';

// Validation schemas
const createTaskSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  dueDate: z.string().optional(), // ISO date string
  parentTaskId: z.string().uuid().optional(),
  priorityScore: z.number().int().min(0).max(100).default(0),
  isHighlight: z.boolean().default(false),
  status: z.enum(["todo", "doing", "done"]).default("todo"),
  toolAction: z.record(z.any()).optional(),
});

const updateTaskSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  parentTaskId: z.string().uuid().optional(),
  priorityScore: z.number().int().min(0).max(100).optional(),
  isHighlight: z.boolean().optional(),
  status: z.enum(["todo", "doing", "done"]).optional(),
  toolAction: z.record(z.any()).optional(),
});

const createMilestoneSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  targetDate: z.string().optional(),
  weight: z.number().int().min(1).max(5).default(3),
});

// AI schemas for auto-plan
const SubtaskSchema = z.object({
  title: z.string().describe('Clear, actionable subtask title'),
  description: z.string().describe('Detailed description of what needs to be done'),
  estimatedTime: z.string().describe('Estimated time to complete (e.g., "30 minutes", "2 hours")'),
  priority: z.number().min(1).max(5).describe('Priority level from 1 (low) to 5 (high)'),
  suggestedTool: z.string().optional().describe('Suggested tool or service to help with this task'),
  dependencies: z.array(z.string()).optional().describe('List of subtask titles this depends on'),
});

const AutoPlanSchema = z.object({
  subtasks: z.array(SubtaskSchema),
  estimatedTotalTime: z.string().describe('Total estimated time for all subtasks'),
  successCriteria: z.array(z.string()).describe('How to know when the main task is complete'),
  tips: z.array(z.string()).optional().describe('Additional tips or considerations'),
});

// AI schemas for task parsing
const ParsedTaskSchema = z.object({
  title: z.string().describe('Clear, actionable task title'),
  description: z.string().optional().describe('Additional context or details'),
  dueDate: z.string().optional().describe('Due date in ISO format if mentioned'),
  estimatedTime: z.string().optional().describe('Estimated time to complete (e.g., "2 hours", "30 minutes")'),
  priority: z.number().min(1).max(5).optional().describe('Priority level from 1 (low) to 5 (high)'),
});

const ParsedTasksSchema = z.object({
  tasks: z.array(ParsedTaskSchema),
  confidence: z.number().min(0).max(1).describe('Confidence level of the parsing'),
});

// Initialize OpenRouter
const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Task Management Procedures
  tasks: router({
    // Create a new task
    create: protectedProcedure
      .input(createTaskSchema)
      .mutation(async ({ input, ctx }) => {
        const [newTask] = await db.insert(tasks).values({
          ...input,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }).returning();
        
        return newTask;
      }),

    // Get all tasks for user with optional filtering
    list: protectedProcedure
      .input(z.object({
        status: z.enum(["todo", "doing", "done"]).optional(),
        isHighlight: z.boolean().optional(),
        parentTaskId: z.string().uuid().optional(),
        includeSubtasks: z.boolean().default(true),
      }).optional())
      .query(async ({ input = {}, ctx }) => {
        // Apply filters
        const conditions = [eq(tasks.userId, ctx.user.id)];
        
        if (input.status) {
          conditions.push(eq(tasks.status, input.status));
        }
        
        if (input.isHighlight !== undefined) {
          conditions.push(eq(tasks.isHighlight, input.isHighlight));
        }
        
        if (input.parentTaskId) {
          conditions.push(eq(tasks.parentTaskId, input.parentTaskId));
        } else if (!input.includeSubtasks) {
          conditions.push(isNull(tasks.parentTaskId));
        }
        
        const result = await db.select().from(tasks)
          .where(and(...conditions))
          .orderBy(desc(tasks.createdAt));
        
        return result;
      }),

    // Get a single task by ID
    getById: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input, ctx }) => {
        const [task] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!task) {
          throw new Error("Task not found");
        }
        
        return task;
      }),

    // Update a task
    update: protectedProcedure
      .input(updateTaskSchema)
      .mutation(async ({ input, ctx }) => {
        const { id, ...updateData } = input;
        
        const [updatedTask] = await db.update(tasks)
          .set({
            ...updateData,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!updatedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return updatedTask;
      }),

    // Delete a task
    delete: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        const [deletedTask] = await db.delete(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!deletedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return { success: true, id: input.id };
      }),

    // Get task hierarchy (parent with all subtasks)
    getHierarchy: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input, ctx }) => {
        // Get the main task
        const [mainTask] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!mainTask) {
          throw new Error("Task not found");
        }
        
        // Get all subtasks
        const subtasks = await db.select().from(tasks)
          .where(and(eq(tasks.parentTaskId, input.id), eq(tasks.userId, ctx.user.id)))
          .orderBy(asc(tasks.createdAt));
        
        return {
          ...mainTask,
          subtasks,
        };
      }),

    // Create multiple tasks (for AI-generated subtasks)
    createMany: protectedProcedure
      .input(z.object({
        tasks: z.array(createTaskSchema),
      }))
      .mutation(async ({ input, ctx }) => {
        const tasksToInsert = input.tasks.map(task => ({
          ...task,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));
        
        const newTasks = await db.insert(tasks).values(tasksToInsert).returning();
        
        return newTasks;
      }),

    // Update task status
    updateStatus: protectedProcedure
      .input(z.object({
        id: z.string().uuid(),
        status: z.enum(["todo", "doing", "done"]),
      }))
      .mutation(async ({ input, ctx }) => {
        const [updatedTask] = await db.update(tasks)
          .set({
            status: input.status,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!updatedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return updatedTask;
      }),

    // Toggle highlight status
    toggleHighlight: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        // First get current state
        const [currentTask] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!currentTask) {
          throw new Error("Task not found");
        }
        
        const [updatedTask] = await db.update(tasks)
          .set({
            isHighlight: !currentTask.isHighlight,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        return updatedTask;
      }),

    // Auto-plan: Generate AI subtasks and save to database
    autoPlan: protectedProcedure
      .input(z.object({
        taskId: z.string().uuid().optional(),
        taskTitle: z.string(),
        taskDescription: z.string().optional(),
        context: z.string().optional(),
        saveToDatabase: z.boolean().default(true),
      }))
      .mutation(async ({ input, ctx }) => {
        try {
          // Generate AI plan
          const result = await generateObject({
            model: openrouter.chat('google/gemini-2.0-flash-exp:free'),
            schema: AutoPlanSchema,
            prompt: `
              Create an Auto Plan for the following task by breaking it down into actionable subtasks.
              
              Task: "${input.taskTitle}"
              Description: "${input.taskDescription || 'No additional description provided'}"
              Context: "${input.context || 'No additional context provided'}"
              
              Guidelines:
              1. Break down the task into 3-8 specific, actionable subtasks
              2. Each subtask should be something that can be completed in one sitting
              3. Order subtasks logically (dependencies should come first)
              4. Provide realistic time estimates
              5. Suggest tools, services, or resources that could help
              6. Include any dependencies between subtasks
              7. Focus on practical steps a solo founder can take
              
              For technical tasks, consider:
              - Research and planning phases
              - Implementation steps
              - Testing and validation
              - Documentation
              
              For business tasks, consider:
              - Preparation and research
              - Execution steps
              - Follow-up actions
              - Tracking and measurement
              
              Make the plan actionable and specific to help the user make progress immediately.
            `,
          });

          const plan = result.object;
          
          if (input.saveToDatabase) {
            // Create the main task if taskId is not provided
            let parentTaskId = input.taskId;
            
            if (!parentTaskId) {
              const [newMainTask] = await db.insert(tasks).values({
                title: input.taskTitle,
                description: input.taskDescription || '',
                userId: ctx.user.id,
                priorityScore: 50, // Default priority
                status: 'todo',
                createdAt: new Date(),
                updatedAt: new Date(),
              }).returning();
              
              parentTaskId = newMainTask.id;
            }

            // Create subtasks in database
            const subtasksToInsert = plan.subtasks.map((subtask, index) => ({
              title: subtask.title,
              description: subtask.description,
              parentTaskId,
              userId: ctx.user.id,
              priorityScore: subtask.priority * 20, // Convert 1-5 scale to 0-100
              status: 'todo' as const,
              toolAction: subtask.suggestedTool ? { 
                tool: subtask.suggestedTool,
                estimatedTime: subtask.estimatedTime,
                dependencies: subtask.dependencies || []
              } : null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }));

            const createdSubtasks = await db.insert(tasks).values(subtasksToInsert).returning();

            return {
              ...plan,
              parentTaskId,
              subtasks: plan.subtasks.map((subtask, index) => ({
                ...subtask,
                id: createdSubtasks[index].id,
                created: true,
              })),
            };
          }

          return plan;
        } catch (error) {
          console.error('Auto Plan error:', error);
          throw new Error(`Failed to generate auto plan: ${error}`);
        }
      }),

    // Parse natural language input into structured tasks
    parseTasks: protectedProcedure
      .input(z.object({
        input: z.string().min(1),
        saveToDatabase: z.boolean().default(true),
      }))
      .mutation(async ({ input, ctx }) => {
        try {
          // Generate AI parsed tasks
          const result = await generateObject({
            model: openrouter.chat('google/gemini-2.0-flash-exp:free'),
            schema: ParsedTasksSchema,
            prompt: `
              Parse the following natural language input into structured tasks for a solo founder's to-do list.
              
              Input: "${input.input}"
              
              Guidelines:
              - Extract clear, actionable tasks
              - Infer due dates from time expressions (e.g., "tomorrow", "next week", "Monday")
              - Estimate time requirements when possible
              - Assign priority based on urgency and importance
              - Break down complex requests into multiple tasks if appropriate
              - For sequential tasks (e.g., "do X then Y"), create separate tasks
              
              Examples:
              - "Finish Stripe onboarding, then email investors tomorrow" → 
                Task 1: "Complete Stripe onboarding setup", priority: 4
                Task 2: "Email update to investors", dueDate: tomorrow, priority: 4
              
              - "Schedule dentist appointment for next week" →
                Task 1: "Schedule dentist appointment", dueDate: next week, priority: 2
              
              Return tasks that are specific, actionable, and helpful for a busy founder.
            `,
          });

          const parsed = result.object;
          
          if (input.saveToDatabase && parsed.tasks.length > 0) {
            // Create tasks in database
            const tasksToInsert = parsed.tasks.map(task => ({
              title: task.title,
              description: task.description || '',
              dueDate: task.dueDate || null,
              userId: ctx.user.id,
              priorityScore: task.priority ? task.priority * 20 : 40, // Convert 1-5 scale to 0-100, default to 40
              status: 'todo' as const,
              toolAction: task.estimatedTime ? { 
                estimatedTime: task.estimatedTime 
              } : null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }));

            const createdTasks = await db.insert(tasks).values(tasksToInsert).returning();

            return {
              ...parsed,
              tasks: parsed.tasks.map((task, index) => ({
                ...task,
                id: createdTasks[index].id,
                created: true,
              })),
            };
          }

          return parsed;
        } catch (error) {
          console.error('Parse tasks error:', error);
          throw new Error(`Failed to parse tasks: ${error}`);
        }
      }),
  }),

  // Milestone Management Procedures
  milestones: router({
    // Create a milestone
    create: protectedProcedure
      .input(createMilestoneSchema)
      .mutation(async ({ input, ctx }) => {
        const [newMilestone] = await db.insert(milestones).values({
          ...input,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }).returning();
        
        return newMilestone;
      }),

    // List all milestones for user
    list: protectedProcedure
      .query(async ({ ctx }) => {
        const result = await db.select().from(milestones)
          .where(eq(milestones.userId, ctx.user.id))
          .orderBy(desc(milestones.createdAt));
        
        return result;
      }),

    // Update a milestone
    update: protectedProcedure
      .input(z.object({
        id: z.string().uuid(),
        title: z.string().min(1).max(255).optional(),
        description: z.string().optional(),
        targetDate: z.string().optional(),
        weight: z.number().int().min(1).max(5).optional(),
      }))
      .mutation(async ({ input, ctx }) => {
        const { id, ...updateData } = input;
        
        const [updatedMilestone] = await db.update(milestones)
          .set({
            ...updateData,
            updatedAt: new Date(),
          })
          .where(and(eq(milestones.id, id), eq(milestones.userId, ctx.user.id)))
          .returning();
        
        if (!updatedMilestone) {
          throw new Error("Milestone not found or unauthorized");
        }
        
        return updatedMilestone;
      }),

    // Delete a milestone
    delete: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        const [deletedMilestone] = await db.delete(milestones)
          .where(and(eq(milestones.id, input.id), eq(milestones.userId, ctx.user.id)))
          .returning();
        
        if (!deletedMilestone) {
          throw new Error("Milestone not found or unauthorized");
        }
        
        return { success: true, id: input.id };
      }),
  }),
});

export type AppRouter = typeof appRouter;

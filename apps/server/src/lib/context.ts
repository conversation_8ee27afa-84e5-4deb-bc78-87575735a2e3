import type { NextRequest } from "next/server";
// import { auth } from "@clerk/nextjs/server";
import { db, users } from "../db";
import { eq } from "drizzle-orm";

export async function createContext(req: NextRequest) {
  try {
    // COMMENTED OUT FOR TESTING - Clerk auth disabled
    // const { userId: clerkId } = await auth();

    // Mock user for testing - replace with actual auth when ready
    const mockClerkId = "test-user-123";

    // if (!clerkId) {
    //   return {
    //     user: null,
    //     session: null,
    //   };
    // }

    // Get or create user in our database
    let [user] = await db.select().from(users).where(eq(users.clerkId, mockClerkId));

    if (!user) {
      // Create a test user for development
      console.log("Creating test user for development...");
      [user] = await db.insert(users).values({
        clerkId: mockClerkId,
        name: "Test User", // Test user
        email: "<EMAIL>", // Test email
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();
      console.log("Test user created:", user);
    }

    return {
      user,
      session: { userId: user.id, clerkId: mockClerkId },
    };
  } catch (error) {
    console.error("Auth error:", error);
    return {
      user: null,
      session: null,
    };
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>;

import type { NextRequest } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db, users } from "../db";
import { eq } from "drizzle-orm";

export async function createContext(req: NextRequest) {
  try {
    const { userId: clerkId } = await auth();
    
    if (!clerkId) {
      return {
        user: null,
        session: null,
      };
    }

    // Get or create user in our database
    let [user] = await db.select().from(users).where(eq(users.clerkId, clerkId));
    
    if (!user) {
      // For now, create a user with basic info
      // In a real app, you'd get this from Clerk's user object
      [user] = await db.insert(users).values({
        clerkId,
        name: "User", // Placeholder - would get from Clerk
        email: "<EMAIL>", // Placeholder - would get from Clerk
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();
    }

    return {
      user,
      session: { userId: user.id, clerkId },
    };
  } catch (error) {
    console.error("Auth error:", error);
    return {
      user: null,
      session: null,
    };
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>;

import type { NextRequest } from "next/server";
// import { auth } from "@clerk/nextjs/server";
import { db, users } from "../db";
import { eq } from "drizzle-orm";

export async function createContext(req: NextRequest) {
  console.log("🔧 createContext called");
  try {
    // COMMENTED OUT FOR TESTING - Clerk auth disabled
    // const { userId: clerkId } = await auth();

    // Mock user for testing - replace with actual auth when ready
    const mockClerkId = "test-user-123";
    console.log("🔑 Using mock clerk ID:", mockClerkId);

    // if (!clerkId) {
    //   return {
    //     user: null,
    //     session: null,
    //   };
    // }

    console.log("🗄️ Querying database for user...");
    // Get or create user in our database
    let [user] = await db.select().from(users).where(eq(users.clerkId, mockClerkId));
    console.log("📊 Database query result:", user ? "User found" : "User not found");

    if (!user) {
      // Create a test user for development
      console.log("👤 Creating test user for development...");
      [user] = await db.insert(users).values({
        clerkId: mockClerkId,
        name: "Test User", // Test user
        email: "<EMAIL>", // Test email
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();
      console.log("✅ Test user created:", user);
    } else {
      console.log("✅ Existing user found:", { id: user.id, name: user.name, email: user.email });
    }

    const context = {
      user,
      session: { userId: user.id, clerkId: mockClerkId },
    };

    console.log("🎯 Context created successfully:", {
      userId: context.user.id,
      sessionUserId: context.session.userId,
      clerkId: context.session.clerkId
    });

    return context;
  } catch (error) {
    console.error("❌ Context creation error:", error);
    console.error("❌ Error details:", {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return {
      user: null,
      session: null,
    };
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>;
